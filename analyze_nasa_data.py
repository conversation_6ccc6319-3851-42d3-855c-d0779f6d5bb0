#!/usr/bin/env python
# coding: utf-8

import pandas as pd
import numpy as np
from collections import defaultdict, Counter

def analyze_nasa_dataset():
    """分析NASA数据集的结构和层次标签分布"""
    
    # 读取数据
    print("正在读取NASA数据集...")
    train_df = pd.read_csv('dataset/nasa_train.csv')
    val_df = pd.read_csv('dataset/nasa_val.csv')
    
    print("=== NASA数据集基本信息 ===")
    print(f"训练集大小: {len(train_df)}")
    print(f"验证集大小: {len(val_df)}")
    print(f"列名: {list(train_df.columns)}")
    print()
    
    # 检查数据完整性
    print("=== 数据完整性检查 ===")
    print("训练集缺失值:")
    print(train_df.isnull().sum())
    print()
    
    # 分析层次标签分布
    print("=== 层次标签统计 ===")
    
    # Cat1 分布
    print("Cat1 (一级分类) 分布:")
    cat1_counts = train_df['Cat1'].value_counts()
    print(cat1_counts)
    print(f"Cat1 总数: {len(cat1_counts)}")
    print()
    
    # Cat2 分布
    print("Cat2 (二级分类) 分布 (前10个):")
    cat2_counts = train_df['Cat2'].value_counts()
    print(cat2_counts.head(10))
    print(f"Cat2 总数: {len(cat2_counts)}")
    print()
    
    # Cat3 分布
    print("Cat3 (三级分类) 分布 (前10个):")
    cat3_counts = train_df['Cat3'].value_counts()
    print(cat3_counts.head(10))
    print(f"Cat3 总数: {len(cat3_counts)}")
    print()
    
    # 构建层次结构
    print("=== 层次结构分析 ===")
    hierarchy = defaultdict(lambda: defaultdict(set))
    
    # 合并训练集和验证集来构建完整的层次结构
    all_data = pd.concat([train_df, val_df], ignore_index=True)
    
    for _, row in all_data.iterrows():
        cat1, cat2, cat3 = row['Cat1'], row['Cat2'], row['Cat3']
        hierarchy[cat1][cat2].add(cat3)
    
    print("完整层次结构:")
    for cat1 in sorted(hierarchy.keys()):
        print(f"{cat1}:")
        for cat2 in sorted(hierarchy[cat1].keys()):
            cat3_list = sorted(list(hierarchy[cat1][cat2]))
            print(f"  {cat2}: {cat3_list}")
        print()
    
    # 统计层次深度
    print("=== 层次深度统计 ===")
    print(f"一级分类数量: {len(hierarchy)}")
    total_cat2 = sum(len(cat2_dict) for cat2_dict in hierarchy.values())
    print(f"二级分类数量: {total_cat2}")
    total_cat3 = sum(len(cat3_set) for cat2_dict in hierarchy.values() for cat3_set in cat2_dict.values())
    print(f"三级分类数量: {total_cat3}")
    
    # 文本长度统计
    print("=== 文本长度统计 ===")
    train_df['text_length'] = train_df['Text'].str.len()
    print(f"文本长度统计:")
    print(f"  平均长度: {train_df['text_length'].mean():.2f}")
    print(f"  中位数长度: {train_df['text_length'].median():.2f}")
    print(f"  最短长度: {train_df['text_length'].min()}")
    print(f"  最长长度: {train_df['text_length'].max()}")
    
    return hierarchy

if __name__ == "__main__":
    hierarchy = analyze_nasa_dataset()
