#!/usr/bin/env python
# coding: utf-8

import csv
from collections import defaultdict

def extract_nasa_hierarchy():
    """从NASA数据集中提取层次结构"""
    
    # 存储层次结构
    hierarchy = defaultdict(lambda: defaultdict(set))
    all_labels = set()
    
    # 读取训练集和验证集
    files = ['dataset/nasa_train.csv', 'dataset/nasa_val.csv']
    
    for file_path in files:
        print(f"处理文件: {file_path}")
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                cat1 = row['Cat1'].strip()
                cat2 = row['Cat2'].strip()
                cat3 = row['Cat3'].strip()
                
                # 添加到层次结构
                hierarchy[cat1][cat2].add(cat3)
                
                # 收集所有标签
                all_labels.add(cat1)
                all_labels.add(cat2)
                all_labels.add(cat3)
    
    print(f"总共发现 {len(all_labels)} 个唯一标签")
    
    # 打印层次结构统计
    print(f"一级分类数量: {len(hierarchy)}")
    total_cat2 = sum(len(cat2_dict) for cat2_dict in hierarchy.values())
    print(f"二级分类数量: {total_cat2}")
    total_cat3 = sum(len(cat3_set) for cat2_dict in hierarchy.values() for cat3_set in cat2_dict.values())
    print(f"三级分类数量: {total_cat3}")
    
    # 生成taxonomy文件
    print("\n生成taxonomy文件...")
    with open('HiAGM-master/data/nasa.taxonomy', 'w', encoding='utf-8') as f:
        # 写入根节点
        cat1_list = sorted(hierarchy.keys())
        f.write('Root\t' + '\t'.join(cat1_list) + '\n')
        
        # 写入一级到二级的映射
        for cat1 in sorted(hierarchy.keys()):
            cat2_list = sorted(hierarchy[cat1].keys())
            if cat2_list:
                f.write(f'{cat1}\t' + '\t'.join(cat2_list) + '\n')
        
        # 写入二级到三级的映射
        for cat1 in sorted(hierarchy.keys()):
            for cat2 in sorted(hierarchy[cat1].keys()):
                cat3_list = sorted(list(hierarchy[cat1][cat2]))
                if cat3_list:
                    f.write(f'{cat2}\t' + '\t'.join(cat3_list) + '\n')
    
    print("taxonomy文件已生成: HiAGM-master/data/nasa.taxonomy")
    
    # 打印层次结构预览
    print("\n=== 层次结构预览 ===")
    for cat1 in sorted(list(hierarchy.keys())[:3]):  # 只显示前3个
        print(f"{cat1}:")
        for cat2 in sorted(list(hierarchy[cat1].keys())[:3]):  # 每个cat1只显示前3个cat2
            cat3_list = sorted(list(hierarchy[cat1][cat2]))[:3]  # 每个cat2只显示前3个cat3
            print(f"  {cat2}: {cat3_list}")
        print()
    
    return hierarchy

if __name__ == "__main__":
    hierarchy = extract_nasa_hierarchy()
